export type User = {
  id: string;
  email: string;
  created_at: string;
};

export type Receipt = {
  id: string;
  user_id: string;
  store_name: string;
  receipt_date: string;
  total_amount: number;
  file_path: string;
  created_at: string;
  updated_at: string;
};

export type ReceiptItem = {
  id: string;
  receipt_id: string;
  name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  category_id: string | null;
  created_at: string;
};

export type Category = {
  id: string;
  name: string;
  user_id: string;
  created_at: string;
};

export type Database = {
  users: User[];
  receipts: Receipt[];
  receipt_items: ReceiptItem[];
  categories: Category[];
};
