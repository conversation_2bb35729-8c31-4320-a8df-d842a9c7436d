"use client";

import { useState, useRef, useEffect } from "react";
import { UploadedPdf } from "./UploadedPdfsTable";
import { useReceipts } from "@/context/ReceiptsContext";

interface UploadButtonProps {
  onPdfUploaded?: (pdf: UploadedPdf) => void; // Make this optional
  buttonText?: string;
  buttonClassName?: string;
  variant?: "button" | "link";
}

export default function UploadButton({
  onPdfUploaded,
  buttonText = "Upload Receipt",
  buttonClassName,
  variant = "button",
}: UploadButtonProps) {
  // Use the receipts context
  const { addReceipt } = useReceipts();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState({ current: 0, total: 0 });
  const [uploadStartTime, setUploadStartTime] = useState<number | null>(null);
  const [showLoadingUI, setShowLoadingUI] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Default class names based on variant
  const defaultButtonClass =
    variant === "button"
      ? "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      : "text-indigo-600 hover:text-indigo-900";

  // Use provided className or default based on variant
  const className = buttonClassName || defaultButtonClass;

  // Show loading UI if upload takes more than 1 second
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (uploading && uploadStartTime) {
      timer = setTimeout(() => {
        setShowLoadingUI(true);
      }, 1000);
    } else {
      setShowLoadingUI(false);
    }

    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [uploading, uploadStartTime]);

  const handleClick = () => {
    // Trigger the hidden file input
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const processFile = async (file: File): Promise<UploadedPdf> => {
    // Check if it's a PDF
    if (
      file.type !== "application/pdf" &&
      file.type !== "" && // Empty type might be a PDF
      !file.name.toLowerCase().endsWith(".pdf")
    ) {
      throw new Error(`${file.name} is not a PDF file`);
    }

    const formData = new FormData();
    formData.append("pdf", file);

    const response = await fetch("/api/parse-pdf", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response
        .text()
        .catch(() => "Could not read error response");
      throw new Error(`Error ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    // Create a new UploadedPdf object
    return {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`, // Generate a unique ID
      fileName: file.name,
      date: new Date(data.date || new Date()), // Use the parsed date or current date if not available
      totalCost: data.totalCost || 0, // Use the parsed total cost or 0 if not available
    };
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    try {
      setUploading(true);
      setUploadStartTime(Date.now());
      setProgress({ current: 0, total: files.length });

      // Process files sequentially to avoid overwhelming the server
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        setProgress({ current: i + 1, total: files.length });

        try {
          const newPdf = await processFile(file);
          // Use the context to add the receipt
          await addReceipt(newPdf);
          // Also call the onPdfUploaded prop if provided (for backward compatibility)
          if (onPdfUploaded) {
            onPdfUploaded(newPdf);
          }
        } catch (fileError: any) {
          console.error(`Error processing ${file.name}:`, fileError);
          // Continue with other files even if one fails
        }
      }
    } catch (err: any) {
      console.error("Error uploading PDFs:", err);
      alert(err.message || "An error occurred during upload");
    } finally {
      setUploading(false);
      setUploadStartTime(null);
      // Reset the file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  return (
    <>
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        accept="application/pdf"
        multiple // Allow multiple file selection
        className="hidden"
      />
      <div className="relative">
        <button
          onClick={handleClick}
          disabled={uploading}
          className={className}
        >
          {uploading
            ? `Processing ${progress.current}/${progress.total}...`
            : buttonText}
        </button>

        {/* Loading UI overlay */}
        {showLoadingUI && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Processing Receipts
              </h3>
              <div className="mb-4">
                <div className="w-full bg-gray-200 rounded-full h-2.5">
                  <div
                    className="bg-indigo-600 h-2.5 rounded-full transition-all duration-300"
                    style={{
                      width: `${(progress.current / progress.total) * 100}%`,
                    }}
                  ></div>
                </div>
                <p className="text-sm text-gray-500 mt-2">
                  Processing file {progress.current} of {progress.total}
                </p>
              </div>
              <p className="text-sm text-gray-500">
                Please wait while we process your receipts. This may take a few
                moments.
              </p>
            </div>
          </div>
        )}
      </div>
    </>
  );
}
