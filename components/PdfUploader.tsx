"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { UploadedPdf } from "./UploadedPdfsTable";

interface PdfUploaderProps {
  onPdfUploaded: (pdf: UploadedPdf) => void;
}

export default function PdfUploader({ onPdfUploaded }: PdfUploaderProps) {
  const [file, setFile] = useState<File | null>(null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [parsedData, setParsedData] = useState<any>(null);
  const router = useRouter();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check if it's a PDF - be more lenient with file types
      // Some browsers/systems might not correctly identify PDFs
      if (
        selectedFile.type !== "application/pdf" &&
        selectedFile.type !== "" && // Empty type might be a PDF
        !selectedFile.name.toLowerCase().endsWith(".pdf")
      ) {
        setError("Please select a PDF file");
        setFile(null);
        return;
      }
      setFile(selectedFile);
      setError(null);
    }
  };

  const handleUpload = async () => {
    if (!file) {
      setError("Please select a file first");
      return;
    }

    try {
      setUploading(true);
      setError(null);
      setParsedData(null);

      const formData = new FormData();
      formData.append("pdf", file);

      const response = await fetch("/api/parse-pdf", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const data = await response.json();
      setParsedData(data);

      // Create a new UploadedPdf object and pass it to the parent component
      const newPdf: UploadedPdf = {
        id: Date.now().toString(), // Generate a unique ID based on timestamp
        fileName: file.name,
        date: new Date(data.date), // Use the parsed date or current date if not available
        totalCost: data.totalCost || 0, // Use the parsed total cost or 0 if not available
      };

      onPdfUploaded(newPdf);
      setUploading(false);

      // Reset the file input after successful upload
      setFile(null);
    } catch (err: any) {
      setError(err.message || "An error occurred during upload");
      setUploading(false);
    }
  };

  return (
    <div className="w-full max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-gray-800">
        Upload Receipt PDF
      </h2>

      <div className="mb-4">
        <label
          className="block text-gray-700 text-sm font-bold mb-2"
          htmlFor="pdf-upload"
        >
          Select PDF File
        </label>
        <input
          id="pdf-upload"
          type="file"
          accept="application/pdf"
          onChange={handleFileChange}
          className="w-full text-sm text-gray-500
            file:mr-4 file:py-2 file:px-4
            file:rounded-md file:border-0
            file:text-sm file:font-semibold
            file:bg-indigo-50 file:text-indigo-700
            hover:file:bg-indigo-100"
        />
        {file && (
          <p className="mt-2 text-sm text-gray-600">
            Selected file: {file.name}
          </p>
        )}
      </div>

      <div>
        <button
          onClick={handleUpload}
          disabled={!file || uploading}
          className="w-full bg-indigo-600 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:bg-indigo-300"
        >
          {uploading ? "Processing..." : "Upload & Parse PDF"}
        </button>
      </div>

      {error && (
        <div className="mt-4 bg-red-50 border-l-4 border-red-500 p-4">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {parsedData && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold mb-2 text-gray-800">
            Parsed Data:
          </h3>
          <div className="bg-gray-50 p-4 rounded overflow-auto max-h-96">
            <pre className="text-sm text-gray-800 whitespace-pre-wrap">
              {JSON.stringify(parsedData, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
