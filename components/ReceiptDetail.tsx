'use client';

import { useEffect, useState } from 'react';
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { UploadedPdf } from './UploadedPdfsTable';

interface ReceiptDetailProps {
  receiptId: string;
}

export default function ReceiptDetail({ receiptId }: ReceiptDetailProps) {
  const [receipt, setReceipt] = useState<UploadedPdf | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load receipts from localStorage
    const storedReceipts = localStorage.getItem('uploadedPdfs');
    if (storedReceipts) {
      try {
        // Parse the stored receipts and convert date strings back to Date objects
        const parsedReceipts = JSON.parse(storedReceipts).map(
          (receipt: {
            id: string;
            fileName: string;
            date: string;
            totalCost: number;
          }) => ({
            ...receipt,
            date: new Date(receipt.date),
          })
        );
        
        // Find the receipt with the matching ID
        const foundReceipt = parsedReceipts.find(r => r.id === receiptId);
        if (foundReceipt) {
          setReceipt(foundReceipt);
        } else {
          // Receipt not found
          notFound();
        }
      } catch (error) {
        console.error('Error parsing stored receipts:', error);
      }
    } else {
      // No receipts found
      notFound();
    }
    
    setLoading(false);
  }, [receiptId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Loading receipt details...</p>
      </div>
    );
  }

  if (!receipt) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-gray-500">Receipt not found</p>
      </div>
    );
  }

  return (
    <div>
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Receipt Details</h1>
          <p className="mt-2 text-sm text-gray-700">
            {receipt.fileName} - {receipt.date.toLocaleDateString()}
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <Link
            href="/dashboard/receipts"
            className="inline-flex items-center justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto mr-2"
          >
            Back to Receipts
          </Link>
        </div>
      </div>

      <div className="mt-8 bg-white shadow overflow-hidden sm:rounded-lg">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Receipt Information
          </h3>
        </div>
        <div className="border-t border-gray-200 px-4 py-5 sm:p-0">
          <dl className="sm:divide-y sm:divide-gray-200">
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">File Name</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {receipt.fileName}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Date</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                {receipt.date.toLocaleDateString()}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Total Amount</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                ${receipt.totalCost.toFixed(2)}
              </dd>
            </div>
            <div className="py-4 sm:py-5 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
              <dt className="text-sm font-medium text-gray-500">Status</dt>
              <dd className="mt-1 text-sm text-gray-900 sm:mt-0 sm:col-span-2">
                <span className="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">
                  Processed
                </span>
              </dd>
            </div>
          </dl>
        </div>
      </div>

      <div className="mt-8 bg-white shadow sm:rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            Items
          </h3>
          <div className="mt-2 max-w-xl text-sm text-gray-500">
            <p>
              Item details are not available in the current version.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
