"use client";

import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
} from "react";
import { UploadedPdf } from "@/components/UploadedPdfsTable";
import {
  getReceipts,
  saveReceipt,
  saveReceipts,
  deleteReceipt,
} from "@/app/actions/receipts";

interface ReceiptsContextType {
  receipts: UploadedPdf[];
  loading: boolean;
  error: Error | null;
  addReceipt: (receipt: UploadedPdf) => Promise<void>;
  addReceipts: (receipts: UploadedPdf[]) => Promise<void>;
  removeReceipt: (id: string) => Promise<void>;
  refreshReceipts: () => Promise<void>;
}

const ReceiptsContext = createContext<ReceiptsContextType | undefined>(
  undefined
);

const INITIAL_RECEIPTS: UploadedPdf[] = [];

export function ReceiptsProvider({
  children,
  initialReceipts = INITIAL_RECEIPTS,
}: {
  children: ReactNode;
  initialReceipts?: UploadedPdf[];
}) {
  const [receipts, setReceipts] = useState<UploadedPdf[]>(initialReceipts);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Initialize receipts from localStorage on mount
  useEffect(() => {
    const loadFromLocalStorage = async () => {
      try {
        setLoading(true);

        // Check if we're in a browser environment
        if (typeof window === "undefined") return;

        const storedReceipts = localStorage.getItem("uploadedPdfs");
        if (storedReceipts) {
          try {
            // Parse the stored receipts and convert date strings back to Date objects
            const parsedReceipts = JSON.parse(storedReceipts).map(
              (receipt: {
                id: string;
                fileName: string;
                date: string;
                totalCost: number;
              }) => ({
                ...receipt,
                date: new Date(receipt.date),
              })
            );
            setReceipts(parsedReceipts);
          } catch (error) {
            console.error("Error parsing stored receipts:", error);
          }
        } else if (initialReceipts.length > 0) {
          // Use initialReceipts if provided and localStorage is empty
          setReceipts(initialReceipts);
        }
      } catch (err) {
        console.error("Error loading receipts from localStorage:", err);
        setError(
          err instanceof Error ? err : new Error("Failed to load receipts")
        );
      } finally {
        setLoading(false);
      }
    };

    loadFromLocalStorage();
  }, [initialReceipts]);

  const refreshReceipts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Get receipts from the repository
      const fetchedReceipts = await getReceipts();
      setReceipts(fetchedReceipts);

      // Update localStorage to ensure consistency
      if (typeof window !== "undefined" && fetchedReceipts.length > 0) {
        localStorage.setItem("uploadedPdfs", JSON.stringify(fetchedReceipts));
      }
    } catch (err) {
      console.error("Error fetching receipts:", err);
      setError(
        err instanceof Error ? err : new Error("Failed to fetch receipts")
      );
    } finally {
      setLoading(false);
    }
  }, []);

  const addReceipt = useCallback(
    async (receipt: UploadedPdf) => {
      try {
        setLoading(true);
        setError(null);

        // Optimistic update
        setReceipts((prevReceipts) => {
          const updatedReceipts = [...prevReceipts, receipt];

          // Update localStorage directly for immediate persistence
          if (typeof window !== "undefined") {
            localStorage.setItem(
              "uploadedPdfs",
              JSON.stringify(updatedReceipts)
            );
          }

          return updatedReceipts;
        });

        // Persist the change to the repository
        await saveReceipt(receipt);
      } catch (err) {
        console.error("Error adding receipt:", err);
        setError(
          err instanceof Error ? err : new Error("Failed to add receipt")
        );

        // Revert optimistic update on error
        await refreshReceipts();
      } finally {
        setLoading(false);
      }
    },
    [refreshReceipts]
  );

  const addReceipts = useCallback(
    async (newReceipts: UploadedPdf[]) => {
      try {
        setLoading(true);
        setError(null);

        // Optimistic update
        setReceipts((prevReceipts) => {
          const updatedReceipts = [...prevReceipts, ...newReceipts];

          // Update localStorage directly for immediate persistence
          if (typeof window !== "undefined") {
            localStorage.setItem(
              "uploadedPdfs",
              JSON.stringify(updatedReceipts)
            );
          }

          return updatedReceipts;
        });

        // Persist the changes to the repository
        // We need to get the updated receipts from the state to ensure we have all receipts
        const allReceipts = await getReceipts();
        await saveReceipts(allReceipts);
      } catch (err) {
        console.error("Error adding receipts:", err);
        setError(
          err instanceof Error ? err : new Error("Failed to add receipts")
        );

        // Revert optimistic update on error
        await refreshReceipts();
      } finally {
        setLoading(false);
      }
    },
    [refreshReceipts]
  );

  const removeReceipt = useCallback(
    async (id: string) => {
      try {
        setLoading(true);
        setError(null);

        // Optimistic update
        setReceipts((prevReceipts) => {
          const updatedReceipts = prevReceipts.filter(
            (receipt) => receipt.id !== id
          );

          // Update localStorage directly for immediate persistence
          if (typeof window !== "undefined") {
            localStorage.setItem(
              "uploadedPdfs",
              JSON.stringify(updatedReceipts)
            );
          }

          return updatedReceipts;
        });

        // Persist the change
        await deleteReceipt(id);
      } catch (err) {
        console.error("Error removing receipt:", err);
        setError(
          err instanceof Error ? err : new Error("Failed to remove receipt")
        );

        // Revert optimistic update on error
        await refreshReceipts();
      } finally {
        setLoading(false);
      }
    },
    [refreshReceipts]
  );

  return (
    <ReceiptsContext.Provider
      value={{
        receipts,
        loading,
        error,
        addReceipt,
        addReceipts,
        removeReceipt,
        refreshReceipts,
      }}
    >
      {children}
    </ReceiptsContext.Provider>
  );
}

export function useReceipts() {
  const context = useContext(ReceiptsContext);
  if (context === undefined) {
    throw new Error("useReceipts must be used within a ReceiptsProvider");
  }
  return context;
}
