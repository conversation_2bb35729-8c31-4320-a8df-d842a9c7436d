"use client";

import ReceiptsTable from "@/components/ReceiptsTable";
import UploadButton from "@/components/UploadButton";
import { useReceipts } from "@/context/ReceiptsContext";
import { useEffect } from "react";

export default function ReceiptsPage() {
  // Use the receipts context
  const { refreshReceipts } = useReceipts();

  // Refresh receipts when the component mounts
  useEffect(() => {
    console.log("Receipts page useEffect");
    refreshReceipts();
  }, [refreshReceipts]);

  return (
    <div>
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <h1 className="text-2xl font-semibold text-gray-900">Receipts</h1>
          <p className="mt-2 text-sm text-gray-700">
            A list of all your uploaded receipts.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 sm:ml-16 sm:flex-none">
          <UploadButton
            buttonText="Upload Receipt"
            buttonClassName="inline-flex items-center justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:w-auto"
          />
        </div>
      </div>

      <ReceiptsTable />
    </div>
  );
}
