import { createServerSupabaseClient } from '@/lib/supabase-server';
import { 
  BarChart, Bar, LineChart, Line, XAxis, YAxis, CartesianGrid, 
  Tooltip, Legend, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell 
} from 'recharts';

export default async function AnalyticsPage() {
  const supabase = createServerSupabaseClient();
  const { data: { user } } = await supabase.auth.getUser();

  if (!user) {
    return null;
  }

  // Get all receipts for the user
  const { data: receipts } = await supabase
    .from('receipts')
    .select('*')
    .eq('user_id', user.id)
    .order('receipt_date');

  // Get all receipt items
  const { data: items } = await supabase
    .from('receipt_items')
    .select('*, receipts!inner(*)')
    .eq('receipts.user_id', user.id);

  // Process data for monthly spending chart
  const monthlySpending = receipts
    ? receipts.reduce((acc: Record<string, number>, receipt) => {
        const month = new Date(receipt.receipt_date).toLocaleString('default', { month: 'short', year: '2-digit' });
        acc[month] = (acc[month] || 0) + receipt.total_amount;
        return acc;
      }, {})
    : {};

  const monthlyChartData = Object.entries(monthlySpending).map(([month, amount]) => ({
    month,
    amount,
  }));

  // Process data for store distribution chart
  const storeDistribution = receipts
    ? receipts.reduce((acc: Record<string, number>, receipt) => {
        acc[receipt.store_name] = (acc[receipt.store_name] || 0) + receipt.total_amount;
        return acc;
      }, {})
    : {};

  const storeChartData = Object.entries(storeDistribution)
    .map(([store, amount]) => ({
      store,
      amount,
    }))
    .sort((a, b) => b.amount - a.amount);

  // Process data for item price evolution
  const itemPriceEvolution: Record<string, { date: string; price: number }[]> = {};
  
  if (items) {
    items.forEach(item => {
      if (!itemPriceEvolution[item.name]) {
        itemPriceEvolution[item.name] = [];
      }
      
      itemPriceEvolution[item.name].push({
        date: new Date(item.receipts.receipt_date).toLocaleDateString(),
        price: item.unit_price,
      });
    });
  }

  // Get top 5 most frequently purchased items
  const itemFrequency: Record<string, number> = {};
  
  if (items) {
    items.forEach(item => {
      itemFrequency[item.name] = (itemFrequency[item.name] || 0) + 1;
    });
  }

  const topItems = Object.entries(itemFrequency)
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 5);

  // Colors for pie chart
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div>
      <h1 className="text-2xl font-semibold text-gray-900">Analytics</h1>
      <p className="mt-2 text-sm text-gray-700">
        Insights from your shopping receipts.
      </p>

      <div className="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
        {/* Monthly Spending Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-medium text-gray-900">Monthly Spending</h2>
          <div className="mt-4 h-80">
            {monthlyChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={monthlyChartData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, 'Amount']} />
                  <Bar dataKey="amount" fill="#4f46e5" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex h-full items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Store Distribution Chart */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-medium text-gray-900">Spending by Store</h2>
          <div className="mt-4 h-80">
            {storeChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={storeChartData}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ store, percent }) => `${store}: ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="amount"
                    nameKey="store"
                  >
                    {storeChartData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip formatter={(value) => [`$${value}`, 'Amount']} />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex h-full items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Top Items */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-medium text-gray-900">Most Purchased Items</h2>
          <div className="mt-4 h-80">
            {topItems.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  layout="vertical"
                  data={topItems}
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis type="number" />
                  <YAxis type="category" dataKey="name" />
                  <Tooltip />
                  <Bar dataKey="count" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex h-full items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </div>
        </div>

        {/* Item Price Evolution */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-medium text-gray-900">Price Evolution</h2>
          <div className="mt-4 h-80">
            {Object.keys(itemPriceEvolution).length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="date" 
                    type="category"
                    allowDuplicatedCategory={false}
                  />
                  <YAxis />
                  <Tooltip formatter={(value) => [`$${value}`, 'Price']} />
                  <Legend />
                  {Object.entries(itemPriceEvolution)
                    .slice(0, 5) // Show only top 5 items for clarity
                    .map(([name, data], index) => (
                      <Line
                        key={name}
                        type="monotone"
                        data={data}
                        dataKey="price"
                        name={name}
                        stroke={COLORS[index % COLORS.length]}
                        activeDot={{ r: 8 }}
                      />
                    ))}
                </LineChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex h-full items-center justify-center">
                <p className="text-gray-500">No data available</p>
              </div>
            )}
          </div>
        </div>
      </div>

      <div className="mt-8 bg-white p-6 rounded-lg shadow">
        <h2 className="text-lg font-medium text-gray-900">Spending Summary</h2>
        <div className="mt-4 grid grid-cols-1 gap-4 sm:grid-cols-3">
          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="text-sm font-medium text-gray-500">Total Spending</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">
              ${receipts?.reduce((sum, receipt) => sum + receipt.total_amount, 0).toFixed(2) || '0.00'}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="text-sm font-medium text-gray-500">Average Receipt</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">
              ${receipts && receipts.length > 0
                ? (receipts.reduce((sum, receipt) => sum + receipt.total_amount, 0) / receipts.length).toFixed(2)
                : '0.00'}
            </p>
          </div>
          <div className="bg-gray-50 p-4 rounded-md">
            <h3 className="text-sm font-medium text-gray-500">Total Receipts</h3>
            <p className="mt-2 text-3xl font-semibold text-gray-900">
              {receipts?.length || 0}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
