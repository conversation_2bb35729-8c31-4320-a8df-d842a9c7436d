/**
 * Type definition for the receipt output format
 */
type ReceiptOutput = {
  company: string;
  receiptId: string;
  date: Date;
  totalCost: number;
  items: {
    name: string;
    units: number;
    pricePerUnit: number;
    totalPrice: number;
  }[];
};

/**
 * Main function to parse a Mercadona receipt
 */
export function parseReceipt(receiptText: string): ReceiptOutput {
  return {
    company: extractCompany(receiptText),
    receiptId: extractReceiptId(receiptText),
    date: extractDate(receiptText),
    totalCost: extractTotalCost(receiptText),
    items: extractItems(receiptText),
  };
}

/**
 * Extracts the company name from receipt text
 */
function extractCompany(text: string): string {
  // Look for the company name at the beginning of the receipt
  const companyRegex = /^([^,]+)/;
  const match = text.match(companyRegex);

  if (match && match[1]) {
    // Return the company name with proper capitalization
    return capitalizeFirstLetters(match[1].trim());
  }

  return "Unknown Company";
}

/**
 * Extracts the receipt ID from receipt text
 */
function extractReceiptId(text: string): string {
  // Look for receipt ID patterns like "FACTURA SIMPLIFICADA: 2786-**********"
  const receiptIdRegex = /FACTURA SIMPLIFICADA:\s*([0-9-]+)/i;
  const match = text.match(receiptIdRegex);

  if (match && match[1]) {
    return match[1].trim();
  }

  // Fallback to operation number if invoice number not found
  const opRegex = /OP:\s*(\d+)/i;
  const opMatch = text.match(opRegex);

  if (opMatch && opMatch[1]) {
    return opMatch[1].trim();
  }

  return "Unknown ID";
}

/**
 * Extracts the date from receipt text
 */
function extractDate(text: string): Date {
  // Look for date patterns like "03/05/2025 16:06"
  const dateRegex = /(\d{2}\/\d{2}\/\d{4})\s*(\d{2}:\d{2})/;
  const match = text.match(dateRegex);

  if (match) {
    const dateStr = match[1] + " " + match[2];
    // Parse the date string into a Date object
    const [day, month, year] = match[1].split("/").map(Number);
    const [hours, minutes] = match[2].split(":").map(Number);

    return new Date(year, month - 1, day, hours, minutes);
  }

  return new Date(); // Return current date as fallback
}

/**
 * Extracts the total cost from receipt text
 */
function extractTotalCost(text: string): number {
  // Look for patterns like "TOTAL (€) 2,37" or "TARJETA BANCARIA 2,37"
  const totalCostRegex = /TOTAL \(€\)\s*(\d+[.,]\d+)/i;
  const match = text.match(totalCostRegex);

  if (match && match[1]) {
    // Convert comma-separated decimal to dot-separated
    return parseFloat(match[1].replace(",", "."));
  }

  // Fallback to other possible total indicators
  const altTotalRegex = /TARJETA BANCARIA\s*(\d+[.,]\d+)/i;
  const altMatch = text.match(altTotalRegex);

  if (altMatch && altMatch[1]) {
    return parseFloat(altMatch[1].replace(",", "."));
  }

  return 0;
}

/**
 * Extracts the items array from receipt text
 */
function extractItems(
  text: string
): Array<{
  name: string;
  units: number;
  pricePerUnit: number;
  totalPrice: number;
}> {
  const items: Array<{
    name: string;
    units: number;
    pricePerUnit: number;
    totalPrice: number;
  }> = [];

  // Split the receipt into lines for analysis
  const lines = text.split("\n");

  // Find the items section boundaries
  let startIndex = -1;
  let endIndex = -1;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();

    // Find where items start - after the header line with "Descripción"
    if (line.includes("Descripción")) {
      startIndex = i + 2; // Skip the header line and the "P. Unit" line
      continue;
    }

    // Find where items end - at the "TOTAL (€)" line
    if (line.startsWith("TOTAL (€)") && startIndex !== -1) {
      endIndex = i;
      break;
    }
  }

  // If we found the items section
  if (startIndex !== -1 && endIndex !== -1) {
    // New pattern detection: When quantity, name, and price are on separate lines
    // This is the case when we have 3 consecutive lines with:
    // Line 1: just a number (quantity)
    // Line 2: product name
    // Line 3: price

    for (let i = startIndex; i < endIndex; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // Pattern 1: Quantity on one line, product name on next, price on third
      if (/^\d+$/.test(line)) {
        // This is a line with just a quantity number
        const units = parseInt(line, 10);

        // Check if next line could be a product name
        if (i + 1 < endIndex && i + 2 < endIndex) {
          const productLine = lines[i + 1].trim();
          const priceLine = lines[i + 2].trim();

          // Check if third line is just a price
          if (/^\d+[.,]\d+$/.test(priceLine)) {
            const name = capitalizeFirstLetters(productLine.toLowerCase());
            const pricePerUnit = parseFloat(priceLine.replace(",", "."));

            items.push({
              name,
              units,
              pricePerUnit,
              totalPrice: pricePerUnit * units,
            });

            // Skip the next two lines since we've processed them
            i += 2;
            continue;
          }
        }
      }

      // Pattern 2: Handle items with multiple units showing total price on the next line
      if (/^\d+\s+/.test(line) && !line.match(/\d+[.,]\d+$/)) {
        // This line contains only quantity and name, but no price
        // Check if the next line has the price info
        if (
          i + 1 < endIndex &&
          /^\d+[.,]\d+\s+\d+[.,]\d+$/.test(lines[i + 1].trim())
        ) {
          const itemMatch = line.match(/(\d+)\s+(.*)/);
          const priceMatch = lines[i + 1]
            .trim()
            .match(/(\d+[.,]\d+)\s+(\d+[.,]\d+)/);

          if (itemMatch && priceMatch) {
            const units = parseInt(itemMatch[1], 10);
            const name = capitalizeFirstLetters(itemMatch[2].toLowerCase());
            const pricePerUnit = parseFloat(priceMatch[1].replace(",", "."));
            const totalPrice = parseFloat(priceMatch[2].replace(",", "."));

            items.push({
              name,
              units,
              pricePerUnit,
              totalPrice,
            });

            // Skip the price line since we've already processed it
            i++;
            continue;
          }
        }
      }

      // Pattern 3: Standard item pattern: "1 DOBLE BURGER 6,00" or "1 12 HUEVOS MEDIANOS-M 2,60"
      // We need to be careful with product names that begin with numbers
      const itemRegex = /^(\d+)\s+(.*)\s+(\d+[.,]\d+)$/;
      const match = line.match(itemRegex);

      if (match) {
        const units = parseInt(match[1], 10);
        const name = capitalizeFirstLetters(match[2].toLowerCase());
        const pricePerUnit = parseFloat(match[3].replace(",", "."));

        items.push({
          name,
          units,
          pricePerUnit,
          totalPrice: pricePerUnit * units, // Calculate total price
        });
      }
    }
  }

  return items;
}

/**
 * Helper function to properly capitalize text
 */
function capitalizeFirstLetters(str: string): string {
  return str.replace(/\b\w/g, (letter) => letter.toUpperCase());
}

// Example usage:
// const receiptText = "MERCADONA, S.A.   A-46103834 ... etc";
// const parsedReceipt = parseReceipt(receiptText);
// console.log(parsedReceipt);
