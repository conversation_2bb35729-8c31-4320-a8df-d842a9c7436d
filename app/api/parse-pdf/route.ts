import { NextRequest, NextResponse } from "next/server";
import PDFParser from "pdf2json";
import { parseR<PERSON>eipt } from "./utils";

export async function POST(request: NextRequest) {
  try {
    console.log("POST request received at /api/parse-pdf");

    let formData;
    try {
      formData = await request.formData();
      console.log("FormData parsed successfully");
    } catch (error) {
      console.error("Error parsing form data:", error);
      return NextResponse.json(
        { error: "Failed to parse form data. Please try again." },
        { status: 400 }
      );
    }

    const file = formData.get("pdf");
    console.log(
      "File from form data:",
      file ? "Found" : "Not found",
      "Type:",
      file ? typeof file : "N/A"
    );

    // Check if a file is provided
    if (!file) {
      console.log("No file provided, returning error");
      return NextResponse.json(
        { error: "No file provided. Please upload a PDF file." },
        { status: 400 }
      );
    }

    // Check if it's a File or Blob object
    if (!(file instanceof Blob)) {
      console.log("File is not a Blob or File object");
      return NextResponse.json(
        { error: "Invalid file object. Please upload a PDF file." },
        { status: 400 }
      );
    }

    // Check if it's a PDF
    console.log("File type:", file.type);
    if (file.type !== "application/pdf" && file.type !== "") {
      console.log("File is not a PDF, returning error");
      return NextResponse.json(
        { error: "Invalid file format. Please upload a PDF file." },
        { status: 400 }
      );
    }

    // Convert the file to a Buffer - no need to write to disk
    console.log("Converting file to buffer, size:", file.size);
    let bytes;
    try {
      bytes = await file.arrayBuffer();
      console.log("File converted to ArrayBuffer, size:", bytes.byteLength);
    } catch (error) {
      console.error("Error converting file to ArrayBuffer:", error);
      return NextResponse.json(
        { error: "Failed to process the file. Please try again." },
        { status: 500 }
      );
    }

    const buffer = Buffer.from(bytes);
    console.log("Buffer created, size:", buffer.length);

    // Parse the PDF using pdf2json
    console.log("Creating PDF parser");
    const pdfParser = new PDFParser();

    // Create a promise to handle the async parsing
    const pdfParsePromise = new Promise<{
      Pages?: Array<{
        Texts?: Array<{
          R?: Array<{
            T?: string;
          }>;
        }>;
      }>;
    }>((resolve, reject) => {
      // Set a timeout to prevent hanging
      const timeout = setTimeout(() => {
        reject(new Error("PDF parsing timed out after 30 seconds"));
      }, 30000);

      // Register error handler
      pdfParser.on("pdfParser_dataError", (errData: { parserError: Error }) => {
        clearTimeout(timeout);
        console.error("PDF parsing error:", errData);
        reject(errData.parserError || new Error("Unknown PDF parsing error"));
      });

      // Register data ready handler
      pdfParser.on("pdfParser_dataReady", (pdfData) => {
        clearTimeout(timeout);
        console.log("PDF parsing completed successfully");
        resolve(pdfData);
      });

      // Parse the PDF from the buffer
      try {
        console.log("Starting PDF parsing");
        pdfParser.parseBuffer(buffer);
      } catch (error) {
        clearTimeout(timeout);
        console.error("Error in parseBuffer:", error);
        reject(error || new Error("Failed to parse PDF buffer"));
      }
    });

    // Wait for the parsing to complete
    let pdfData;
    try {
      pdfData = await pdfParsePromise;
      console.log(
        "PDF data received:",
        !!pdfData,
        "Pages:",
        pdfData?.Pages?.length || 0
      );
    } catch (error) {
      console.error("PDF parsing promise rejected:", error);
      return NextResponse.json(
        {
          error:
            "Failed to parse the PDF file. The file may be corrupted or password-protected.",
          details: error instanceof Error ? error.message : String(error),
        },
        { status: 422 }
      );
    }

    // Extract text from the PDF data
    let extractedText = "";

    try {
      console.log("Extracting text from PDF data");
      if (pdfData && pdfData.Pages) {
        for (const page of pdfData.Pages) {
          if (page.Texts) {
            for (const textItem of page.Texts) {
              if (textItem.R && textItem.R.length > 0) {
                for (const r of textItem.R) {
                  if (r.T) {
                    try {
                      // Decode URI encoded text
                      extractedText += decodeURIComponent(r.T) + " ";
                    } catch (decodeError) {
                      // If decoding fails, use the raw text
                      console.warn("Failed to decode URI component:", r.T);
                      extractedText += r.T + " ";
                    }
                  }
                }
                extractedText += "\n";
              }
            }
          }
        }
      }

      console.log("Extracted text length:", extractedText.length);
      if (extractedText.length < 1000) {
        console.log("Extracted text:", extractedText);
      } else {
        console.log(
          "Extracted text (first 1000 chars):",
          extractedText.substring(0, 1000)
        );
      }
    } catch (error) {
      console.error("Error extracting text from PDF data:", error);
      return NextResponse.json(
        {
          error: "Failed to extract text from the PDF file.",
          details: error instanceof Error ? error.message : String(error),
        },
        { status: 422 }
      );
    }

    // Check if we were able to extract text from the PDF
    if (!extractedText || extractedText.trim() === "") {
      console.log("No text extracted from PDF, using fallback mechanism");

      // Create a fallback response with basic information
      // This allows the app to continue working even if PDF parsing fails
      const fileName = file instanceof File ? file.name : "unknown";
      const fallbackData = {
        company: "Unknown Store",
        receiptId: "Unknown ID",
        date: new Date(),
        totalCost: 0,
        items: [],
        debug: {
          textLength: 0,
          fileType: file.type,
          fileName: fileName,
          hasPdfData: !!pdfData,
          pageCount: pdfData?.Pages?.length || 0,
          fallback: true,
          error: "Could not extract text from the PDF",
        },
      };

      console.log("Returning fallback data:", fallbackData);
      return NextResponse.json(fallbackData);
    }

    // Extract receipt data
    console.log("Parsing receipt data");
    let receiptData;
    try {
      receiptData = parseReceipt(extractedText);
      console.log(
        "Receipt data parsed successfully:",
        "Company:",
        receiptData.company,
        "Date:",
        receiptData.date,
        "Total:",
        receiptData.totalCost
      );
    } catch (error) {
      console.error("Error parsing receipt data:", error);

      // Create a fallback response with basic information from the file
      // This allows the app to continue working even if receipt parsing fails
      const fileName = file instanceof File ? file.name : "unknown";

      // Try to extract a date from the filename if possible
      let fileDate = new Date();
      const dateMatch = fileName.match(/(\d{1,2})[-_](\d{1,2})[-_](\d{2,4})/);
      if (dateMatch) {
        const [_, day, month, year] = dateMatch;
        const fullYear = year.length === 2 ? `20${year}` : year;
        fileDate = new Date(`${fullYear}-${month}-${day}`);
      }

      receiptData = {
        company: "Unknown Store",
        receiptId: "Unknown ID",
        date: fileDate,
        totalCost: 0,
        items: [],
        debug: {
          textLength: extractedText.length,
          fileType: file.type,
          fileName: fileName,
          rawTextSample: extractedText.substring(0, 1000),
          parsingError: error instanceof Error ? error.message : String(error),
          fallback: true,
        },
      };

      console.log("Using fallback receipt data due to parsing error");
    }

    console.log("Returning successful response");
    return NextResponse.json({
      ...receiptData,
      debug: {
        textLength: extractedText ? extractedText.length : 0,
        fileType: file.type,
        fileName: file instanceof File ? file.name : "unknown",
        hasPdfData: !!pdfData,
        pageCount: pdfData?.Pages?.length || 0,
      },
    });
  } catch (error) {
    console.error("Unhandled error processing PDF:", error);
    const errorMessage =
      error instanceof Error ? error.message : "Unknown error processing PDF";
    return NextResponse.json(
      {
        error: errorMessage,
        details: "An unexpected error occurred while processing the PDF file.",
      },
      { status: 500 }
    );
  }
}
