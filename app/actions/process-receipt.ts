'use server';

import { createServerSupabaseClient } from '@/lib/supabase-server';
import { parseReceiptPDF } from '@/lib/pdf-parser';
import { revalidatePath } from 'next/cache';

export async function processReceipt(receiptId: string) {
  const supabase = createServerSupabaseClient();
  
  try {
    // Get the receipt record
    const { data: receipt, error: receiptError } = await supabase
      .from('receipts')
      .select('*')
      .eq('id', receiptId)
      .single();
    
    if (receiptError || !receipt) {
      throw new Error('Receipt not found');
    }
    
    // Parse the PDF
    const parsedData = await parseReceiptPDF(receipt.file_path);
    
    // Update the receipt with parsed data
    const { error: updateError } = await supabase
      .from('receipts')
      .update({
        store_name: parsedData.storeName,
        receipt_date: parsedData.date,
        total_amount: parsedData.totalAmount,
      })
      .eq('id', receiptId);
    
    if (updateError) {
      throw updateError;
    }
    
    // Insert the parsed items
    if (parsedData.items.length > 0) {
      const items = parsedData.items.map(item => ({
        receipt_id: receiptId,
        name: item.name,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        total_price: item.totalPrice,
      }));
      
      const { error: itemsError } = await supabase
        .from('receipt_items')
        .insert(items);
      
      if (itemsError) {
        throw itemsError;
      }
    }
    
    // Revalidate the receipts page to show updated data
    revalidatePath('/dashboard/receipts');
    
    return { success: true };
  } catch (error: any) {
    console.error('Error processing receipt:', error);
    return { success: false, error: error.message };
  }
}
