'use server';

import { getReceiptRepository } from "@/lib/receipts";
import { UploadedPdf } from "@/components/UploadedPdfsTable";

export async function getReceipts(): Promise<UploadedPdf[]> {
  const repository = getReceiptRepository();
  return repository.getReceipts();
}

export async function saveReceipt(receipt: UploadedPdf): Promise<void> {
  const repository = getReceiptRepository();
  return repository.saveReceipt(receipt);
}

export async function saveReceipts(receipts: UploadedPdf[]): Promise<void> {
  const repository = getReceiptRepository();
  return repository.saveReceipts(receipts);
}

export async function deleteReceipt(id: string): Promise<void> {
  const repository = getReceiptRepository();
  return repository.deleteReceipt(id);
}
