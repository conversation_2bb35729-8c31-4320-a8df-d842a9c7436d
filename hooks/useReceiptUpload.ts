'use client';

import { useState, useRef } from 'react';
import { UploadedPdf } from '@/components/UploadedPdfsTable';
import { useReceipts } from '@/context/ReceiptsContext';

interface UseReceiptUploadOptions {
  onUploadStart?: () => void;
  onUploadComplete?: (receipts: UploadedPdf[]) => void;
  onUploadError?: (error: Error) => void;
}

export function useReceiptUpload(options: UseReceiptUploadOptions = {}) {
  const { addReceipt, addReceipts } = useReceipts();
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState({ current: 0, total: 0 });
  const [uploadStartTime, setUploadStartTime] = useState<number | null>(null);
  const [showLoadingUI, setShowLoadingUI] = useState(false);
  
  // Set up a timer to show loading UI if upload takes more than 1 second
  const loadingTimerRef = useRef<NodeJS.Timeout | null>(null);

  const startLoadingTimer = () => {
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
    }
    
    loadingTimerRef.current = setTimeout(() => {
      setShowLoadingUI(true);
    }, 1000);
  };

  const clearLoadingTimer = () => {
    if (loadingTimerRef.current) {
      clearTimeout(loadingTimerRef.current);
      loadingTimerRef.current = null;
    }
  };

  const processFile = async (file: File): Promise<UploadedPdf> => {
    // Check if it's a PDF
    if (
      file.type !== 'application/pdf' &&
      file.type !== '' && // Empty type might be a PDF
      !file.name.toLowerCase().endsWith('.pdf')
    ) {
      throw new Error(`${file.name} is not a PDF file`);
    }

    const formData = new FormData();
    formData.append('pdf', file);

    const response = await fetch('/api/parse-pdf', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text().catch(() => 'Could not read error response');
      throw new Error(`Error ${response.status}: ${errorText}`);
    }

    const data = await response.json();

    // Create a new UploadedPdf object
    return {
      id: `${Date.now()}-${Math.random().toString(36).substring(2, 9)}`, // Generate a unique ID
      fileName: file.name,
      date: new Date(data.date || new Date()), // Use the parsed date or current date if not available
      totalCost: data.totalCost || 0, // Use the parsed total cost or 0 if not available
    };
  };

  const uploadFiles = async (files: FileList | File[]) => {
    if (!files || files.length === 0) return;

    try {
      setUploading(true);
      setUploadStartTime(Date.now());
      setProgress({ current: 0, total: files.length });
      startLoadingTimer();
      
      options.onUploadStart?.();

      const uploadedReceipts: UploadedPdf[] = [];

      // Process files sequentially to avoid overwhelming the server
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        setProgress({ current: i + 1, total: files.length });
        
        try {
          const newPdf = await processFile(file);
          uploadedReceipts.push(newPdf);
          
          // If it's a single file, use addReceipt for better optimistic updates
          if (files.length === 1) {
            await addReceipt(newPdf);
          }
        } catch (fileError: any) {
          console.error(`Error processing ${file.name}:`, fileError);
          // Continue with other files even if one fails
        }
      }

      // If multiple files were uploaded, add them all at once
      if (files.length > 1 && uploadedReceipts.length > 0) {
        await addReceipts(uploadedReceipts);
      }

      options.onUploadComplete?.(uploadedReceipts);
    } catch (err: any) {
      console.error('Error uploading PDFs:', err);
      const error = err instanceof Error ? err : new Error(err.message || 'An error occurred during upload');
      options.onUploadError?.(error);
    } finally {
      setUploading(false);
      setUploadStartTime(null);
      setShowLoadingUI(false);
      clearLoadingTimer();
    }
  };

  return {
    uploading,
    progress,
    showLoadingUI,
    uploadFiles
  };
}
