/**
 * Test utilities for chart navigation logic
 * This file contains test functions to verify the navigation behavior
 */

// Test function to verify date range calculation
export function testDateRangeCalculation() {
  const now = new Date('2024-12-15'); // Fixed date for testing
  
  // Test current period (offset = 0)
  const currentPeriod = getDateRangeForOffset(now, 0);
  console.log('Current period (offset 0):', currentPeriod);
  // Should show: Jan 2024 - Dec 2024
  
  // Test 6 months back (offset = 6)
  const sixMonthsBack = getDateRangeForOffset(now, 6);
  console.log('6 months back (offset 6):', sixMonthsBack);
  // Should show: Jul 2023 - Jun 2024
  
  // Test 12 months back (offset = 12)
  const twelveMonthsBack = getDateRangeForOffset(now, 12);
  console.log('12 months back (offset 12):', twelveMonthsBack);
  // Should show: Jan 2023 - Dec 2023
}

// Helper function to calculate date range for a given offset
function getDateRangeForOffset(now: Date, timelineOffset: number): string {
  const endDate = new Date(now.getFullYear(), now.getMonth() - timelineOffset, 1);
  const startDate = new Date(now.getFullYear(), now.getMonth() - 11 - timelineOffset, 1);
  
  const formatDate = (date: Date) => date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short' 
  });
  
  return `${formatDate(startDate)} - ${formatDate(endDate)}`;
}

// Test function to verify month generation
export function testMonthGeneration() {
  const now = new Date('2024-12-15');
  
  console.log('Testing month generation for different offsets:');
  
  for (let offset = 0; offset <= 12; offset += 6) {
    console.log(`\nOffset ${offset}:`);
    const months = generateMonthsForOffset(now, offset);
    console.log('Months:', months.map(m => m.monthKey).join(', '));
    console.log('Date range:', getDateRangeForOffset(now, offset));
  }
}

// Helper function to generate months for a given offset
function generateMonthsForOffset(now: Date, timelineOffset: number) {
  const months = [];
  
  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i - timelineOffset, 1);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    months.push({ monthKey, date });
  }
  
  return months;
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  console.log('Chart Navigation Tests');
  console.log('=====================');
  testDateRangeCalculation();
  testMonthGeneration();
}
