import { UploadedPdf } from "@/components/UploadedPdfsTable";
import { ReceiptRepository } from "./types";

export class LocalStorageReceiptRepository implements ReceiptRepository {
  private readonly STORAGE_KEY = "uploadedPdfs";

  async getReceipts(): Promise<UploadedPdf[]> {
    // We need to check if we're in a browser environment
    if (typeof window === "undefined") {
      return [];
    }

    const storedReceipts = localStorage.getItem(this.STORAGE_KEY);
    if (!storedReceipts) {
      return [];
    }

    try {
      // Parse the stored receipts and convert date strings back to Date objects
      return JSON.parse(storedReceipts).map(
        (receipt: {
          id: string;
          fileName: string;
          date: string;
          totalCost: number;
        }) => ({
          ...receipt,
          date: new Date(receipt.date),
        })
      );
    } catch (error) {
      console.error("Error parsing stored receipts:", error);
      return [];
    }
  }

  async saveReceipt(receipt: UploadedPdf): Promise<void> {
    // We need to check if we're in a browser environment
    if (typeof window === "undefined") {
      return;
    }

    const receipts = await this.getReceipts();

    // Check if the receipt already exists
    const existingIndex = receipts.findIndex((r) => r.id === receipt.id);

    if (existingIndex >= 0) {
      // Update existing receipt
      receipts[existingIndex] = receipt;
    } else {
      // Add new receipt
      receipts.push(receipt);
    }

    // Save back to localStorage
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(receipts));
  }

  async saveReceipts(receipts: UploadedPdf[]): Promise<void> {
    // We need to check if we're in a browser environment
    if (typeof window === "undefined") {
      return;
    }

    // Get existing receipts
    const existingReceipts = await this.getReceipts();

    // Create a map of existing receipts by ID for quick lookup
    const existingReceiptsMap = new Map(
      existingReceipts.map((receipt) => [receipt.id, receipt])
    );

    // Merge new receipts with existing ones, updating any that have the same ID
    for (const receipt of receipts) {
      existingReceiptsMap.set(receipt.id, receipt);
    }

    // Convert the map back to an array
    const mergedReceipts = Array.from(existingReceiptsMap.values());

    // Save to localStorage
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(mergedReceipts));
  }

  async deleteReceipt(id: string): Promise<void> {
    // We need to check if we're in a browser environment
    if (typeof window === "undefined") {
      return;
    }

    const receipts = await this.getReceipts();
    const filteredReceipts = receipts.filter((receipt) => receipt.id !== id);

    // Save back to localStorage
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(filteredReceipts));
  }
}
