import PDFParser from "pdf2json";
import { supabase } from "./supabase";

interface ParsedReceipt {
  storeName: string;
  date: string;
  totalAmount: number;
  items: {
    name: string;
    quantity: number;
    unitPrice: number;
    totalPrice: number;
  }[];
}

export async function parseReceiptPDF(
  filePath: string
): Promise<ParsedReceipt> {
  try {
    // Download the file from Supabase Storage
    const { data, error } = await supabase.storage
      .from("receipts")
      .download(filePath);

    if (error) {
      throw error;
    }

    if (!data) {
      throw new Error("Could not download the receipt file");
    }

    // Parse the PDF using pdf2json
    const pdfParser = new PDFParser();

    // Create a promise to handle the async parsing
    const text = await new Promise<string>((resolve, reject) => {
      pdfParser.on("pdfParser_dataError", (errData: { parserError: Error }) => {
        reject(errData.parserError);
      });

      pdfParser.on("pdfParser_dataReady", (pdfData) => {
        // Convert the PDF data to text
        try {
          // Access the parsed data and convert it to text
          let text = "";

          // pdf2json stores text in Pages array
          if (pdfData && pdfData.Pages) {
            for (const page of pdfData.Pages) {
              if (page.Texts) {
                for (const textItem of page.Texts) {
                  if (textItem.R && textItem.R.length > 0) {
                    for (const r of textItem.R) {
                      if (r.T) {
                        // Decode URI encoded text
                        text += decodeURIComponent(r.T) + " ";
                      }
                    }
                    text += "\n";
                  }
                }
              }
            }
          }

          console.log(
            "Extracted text from PDF parser:",
            text.substring(0, 100) + "..."
          );
          resolve(text || "");
        } catch (err) {
          console.error("Error extracting text:", err);
          resolve(""); // Resolve with empty string on error
        }
      });

      // Parse the PDF from the buffer
      pdfParser.parseBuffer(data);
    });

    // This is a simplified parsing logic
    // In a real application, you would need more sophisticated parsing
    // based on the specific format of the receipts you're targeting

    // pdf2json replaces spaces with non-breaking spaces and has different formatting
    // We need to clean up the text first
    const cleanText = text
      .replace(/\s+/g, " ") // Normalize whitespace
      .replace(/\\n/g, "\n") // Handle newlines
      .replace(/\\\//g, "/") // Handle escaped slashes
      .replace(/\\\\/g, "\\") // Handle escaped backslashes
      .replace(/\\/g, "") // Remove remaining backslashes
      .replace(/\u00A0/g, " "); // Replace non-breaking spaces with regular spaces

    // For Mercadona receipts, try to extract store name
    let storeName = "Unknown Store";
    if (cleanText.includes("MERCADONA") || cleanText.includes("Mercadona")) {
      storeName = "Mercadona";
    } else {
      // Generic store name extraction
      const storeNameMatch = cleanText.match(
        /(?:Store|Merchant|Retailer):\s*([^\n]+)/i
      );
      if (storeNameMatch) {
        storeName = storeNameMatch[1].trim();
      }
    }

    // Extract date - try Mercadona format first, then generic
    let date = new Date().toISOString().split("T")[0];

    // Try to find date in format "DD/MM/YY" or "DD/MM/YYYY"
    const mercadonaDateMatch = cleanText.match(
      /(\d{1,2})\/(\d{1,2})\/(\d{2,4})/
    );
    if (mercadonaDateMatch) {
      const day = mercadonaDateMatch[1].padStart(2, "0");
      const month = mercadonaDateMatch[2].padStart(2, "0");
      const year =
        mercadonaDateMatch[3].length === 2
          ? `20${mercadonaDateMatch[3]}`
          : mercadonaDateMatch[3];
      date = `${day}/${month}/${year}`;
    } else {
      // Try generic date format
      const dateMatch = cleanText.match(
        /(?:Date|Time):\s*([0-9]{1,2}[\/\-][0-9]{1,2}[\/\-][0-9]{2,4})/i
      );
      if (dateMatch) {
        date = dateMatch[1];
      }
    }

    // Extract total amount - try Mercadona format first, then generic
    let totalAmount = 0;

    // Try to find "TOTAL" followed by a number with comma as decimal separator
    const mercadonaTotalMatch = cleanText.match(/TOTAL\s+(\d+[,\.]\d+)/i);
    if (mercadonaTotalMatch) {
      // Replace comma with dot for proper parsing
      totalAmount = parseFloat(mercadonaTotalMatch[1].replace(",", "."));
    } else {
      // Try generic total format
      const totalMatch = cleanText.match(
        /(?:Total|Amount|Sum):\s*\$?([0-9]+\.[0-9]{2})/i
      );
      if (totalMatch) {
        totalAmount = parseFloat(totalMatch[1]);
      }
    }

    // Extract items (this is highly simplified)
    // In reality, this would require more sophisticated pattern matching
    const items = [];

    // For Mercadona receipts, try to extract items
    // Mercadona format typically has product name followed by price
    // We'll try to identify patterns in the text

    // Split the text into lines
    const lines = cleanText.split("\n");

    // Process each line
    for (let i = 0; i < lines.length; i++) {
      const line = lines[i].trim();

      // Skip empty lines
      if (!line) continue;

      // Try to match Mercadona item format
      // Look for lines with a price at the end (numbers with comma as decimal separator)
      const mercadonaItemMatch = line.match(/^(.+?)\s+(\d+[,\.]\d+)$/);

      if (mercadonaItemMatch) {
        const name = mercadonaItemMatch[1].trim();
        const price = parseFloat(mercadonaItemMatch[2].replace(",", "."));

        // Try to find quantity in the next line if it exists
        let quantity = 1;
        let unitPrice = price;

        if (i + 1 < lines.length) {
          const nextLine = lines[i + 1].trim();
          // Look for quantity x unit price format
          const quantityMatch = nextLine.match(/(\d+)\s*[xX]\s*(\d+[,\.]\d+)/);

          if (quantityMatch) {
            quantity = parseInt(quantityMatch[1], 10);
            unitPrice = parseFloat(quantityMatch[2].replace(",", "."));
            i++; // Skip the next line since we've processed it
          }
        }

        items.push({
          name,
          quantity,
          unitPrice,
          totalPrice: price,
        });
      } else {
        // Try generic item format as fallback
        const genericItemMatch = line.match(
          /([A-Za-z0-9\s]+)\s+(\d+)\s+\$?(\d+[,\.]\d+)\s+\$?(\d+[,\.]\d+)/
        );

        if (genericItemMatch) {
          items.push({
            name: genericItemMatch[1].trim(),
            quantity: parseInt(genericItemMatch[2], 10),
            unitPrice: parseFloat(genericItemMatch[3].replace(",", ".")),
            totalPrice: parseFloat(genericItemMatch[4].replace(",", ".")),
          });
        }
      }
    }

    return {
      storeName,
      date,
      totalAmount,
      items,
    };
  } catch (error) {
    console.error("Error parsing PDF:", error);
    // Return default values if parsing fails
    return {
      storeName: "Unknown Store",
      date: new Date().toISOString().split("T")[0],
      totalAmount: 0,
      items: [],
    };
  }
}
